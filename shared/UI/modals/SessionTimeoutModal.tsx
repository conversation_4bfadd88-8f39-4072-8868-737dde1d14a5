// shared/UI/modals/SessionTimeoutModal.tsx
"use client";

import React from 'react';
import BaseModal from './BaseModal';
import { useLogoutHandler } from '@/shared/hooks/business/useLogoutHandler';

interface SessionTimeoutModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  message?: string;
}

/**
 * Session Timeout Modal Component
 * 
 * A modal that appears when the user's session has expired.
 * Features:
 * - Clean, minimal design
 * - Non-dismissible (user must take action)
 * - Single "Sign In" button that logs out and redirects
 * - Clicking close button or backdrop also logs out and redirects
 * - Consistent with existing modal design system
 */
export const SessionTimeoutModal: React.FC<SessionTimeoutModalProps> = ({
  isOpen,
  onClose,
  title = "Session Expired",
  message = "Your session has expired. Please sign in again."
}) => {
  const { handleLogout, isLoggingOut } = useLogoutHandler();

  /**
   * Handle sign in button click
   * This will trigger logout and redirect to sign-in page
   */
  const handleSignIn = () => {
    handleLogout();
    // The useLogoutHandler will handle the redirect to sign-in page
  };

  /**
   * Handle modal close (X button or backdrop click)
   * Same behavior as sign in button - logout and redirect
   */
  const handleModalClose = () => {
    if (onClose) {
      onClose();
    }
    handleLogout();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleModalClose}
      title={title}
      size="sm"
      position="center"
      animation="scale"
      closeOnBackdropClick={true}
      closeOnEscape={true}
      showCloseButton={true}
      zIndex={10000}
      className="session-timeout-modal"
      headerClassName="bg-danger/10 border-danger/20"
      bodyClassName="text-center py-6"
      footerClassName="border-t-0 pb-6"
      footer={
        <div className="flex justify-center w-full">
          <button
            onClick={handleSignIn}
            disabled={isLoggingOut}
            className={`
              px-6 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
              ${isLoggingOut
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-primary text-white hover:bg-primary/90 focus:ring-2 focus:ring-primary/50'
              }
              focus:outline-none focus:ring-offset-2 focus:ring-offset-table-section
              min-w-[120px] flex items-center justify-center gap-2
            `}
          >
            {isLoggingOut ? (
              <>
                <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Signing Out...</span>
              </>
            ) : (
              'Sign In'
            )}
          </button>
        </div>
      }
      header={
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-danger/20 flex items-center justify-center">
            <i className="ri-time-line text-danger text-lg"></i>
          </div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            {title}
          </h3>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Icon */}
        <div className="flex justify-center">
          <div className="w-16 h-16 rounded-full bg-danger/10 flex items-center justify-center">
            <i className="ri-time-line text-danger text-3xl"></i>
          </div>
        </div>

        {/* Message */}
        <div className="space-y-2">
          <p className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
            {message}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            For your security, you have been automatically signed out due to inactivity.
          </p>
        </div>
      </div>
    </BaseModal>
  );
};

export default SessionTimeoutModal;
