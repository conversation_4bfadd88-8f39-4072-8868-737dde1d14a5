// shared/providers/SessionTimeoutProvider.tsx
"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useSessionTimeout } from '@/shared/hooks/business/useSessionTimeout';
import { SessionTimeoutModal } from '@/shared/UI/modals/SessionTimeoutModal';
import { useAuthStore } from '@/shared/stores/authStore';

interface SessionTimeoutContextType {
  isSessionExpired: boolean;
  remainingTime: number;
  isWarningShown: boolean;
  resetTimeout: () => void;
  showWarning: (remainingTime: number) => void;
  hideWarning: () => void;
}

const SessionTimeoutContext = createContext<SessionTimeoutContextType | undefined>(undefined);

interface SessionTimeoutProviderProps {
  children: React.ReactNode;
  warningThreshold?: number; // Minutes before timeout to show warning
  enabled?: boolean;
}

/**
 * Session Timeout Provider
 * 
 * Provides global session timeout management for the entire application.
 * Features:
 * - Automatic session timeout based on betshop settings
 * - Activity tracking across the entire app
 * - Session expired modal
 * - Optional warning before timeout
 * - Integration with existing auth system
 */
export const SessionTimeoutProvider: React.FC<SessionTimeoutProviderProps> = ({
  children,
  warningThreshold = 2, // 2 minutes warning by default
  enabled = true
}) => {
  const { isAuthenticated } = useAuthStore();
  const [isSessionExpired, setIsSessionExpired] = useState(false);
  const [isWarningShown, setIsWarningShown] = useState(false);
  const [_warningRemainingTime, setWarningRemainingTime] = useState(0);

  /**
   * Handle session timeout
   */
  const handleTimeout = useCallback(() => {
    setIsSessionExpired(true);
    setIsWarningShown(false);
  }, []);

  /**
   * Handle warning threshold reached
   */
  const handleWarning = useCallback((remainingTime: number) => {
    setIsWarningShown(true);
    setWarningRemainingTime(remainingTime);
  }, []);

  /**
   * Show warning manually
   */
  const showWarning = useCallback((remainingTime: number) => {
    setIsWarningShown(true);
    setWarningRemainingTime(remainingTime);
  }, []);

  /**
   * Hide warning
   */
  const hideWarning = useCallback(() => {
    setIsWarningShown(false);
    setWarningRemainingTime(0);
  }, []);

  /**
   * Handle modal close
   */
  const handleModalClose = useCallback(() => {
    setIsSessionExpired(false);
    setIsWarningShown(false);
  }, []);

  // Initialize session timeout hook
  const sessionTimeout = useSessionTimeout({
    onTimeout: handleTimeout,
    onWarning: handleWarning,
    warningThreshold,
    enabled: enabled && isAuthenticated,
  });

  // Reset session expired state when user logs out or logs in
  useEffect(() => {
    if (!isAuthenticated) {
      setIsSessionExpired(false);
      setIsWarningShown(false);
    }
  }, [isAuthenticated]);

  // Context value
  const contextValue: SessionTimeoutContextType = {
    isSessionExpired,
    remainingTime: sessionTimeout.remainingTime,
    isWarningShown,
    resetTimeout: sessionTimeout.resetTimeout,
    showWarning,
    hideWarning,
  };

  return (
    <SessionTimeoutContext.Provider value={contextValue}>
      {children}

      {/* Session Expired Modal */}
      <SessionTimeoutModal
        isOpen={isSessionExpired}
        onClose={handleModalClose}
        title="Session Expired"
        message="Your session has expired. Please sign in again."
      />
    </SessionTimeoutContext.Provider>
  );
};

/**
 * Hook to use session timeout context
 */
export const useSessionTimeoutContext = (): SessionTimeoutContextType => {
  const context = useContext(SessionTimeoutContext);

  if (context === undefined) {
    throw new Error('useSessionTimeoutContext must be used within a SessionTimeoutProvider');
  }

  return context;
};

/**
 * Optional warning modal component (can be used separately if needed)
 */
interface SessionWarningModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExtendSession: () => void;
  remainingTime: number;
}

export const SessionWarningModal: React.FC<SessionWarningModalProps> = ({
  isOpen,
  onClose,
  onExtendSession: _onExtendSession,
  remainingTime
}) => {
  const minutes = Math.ceil(remainingTime / (1000 * 60));

  return (
    <SessionTimeoutModal
      isOpen={isOpen}
      onClose={onClose}
      title="Session Warning"
      message={`Your session will expire in ${minutes} minute${minutes !== 1 ? 's' : ''}. Would you like to extend your session?`}
    />
  );
};

export default SessionTimeoutProvider;
