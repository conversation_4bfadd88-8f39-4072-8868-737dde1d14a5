// shared/hooks/business/useSessionTimeout.ts
import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { useCashierWebTimeout } from './useBetshopSettings';

interface SessionTimeoutConfig {
  onTimeout: () => void;
  onWarning?: (remainingTime: number) => void;
  warningThreshold?: number; // Minutes before timeout to show warning
  enabled?: boolean;
}

interface SessionTimeoutState {
  isActive: boolean;
  remainingTime: number; // in milliseconds
  isWarningShown: boolean;
  lastActivity: number;
}

/**
 * Hook for managing session timeout with activity tracking
 */
export const useSessionTimeout = (config: SessionTimeoutConfig) => {
  const { onTimeout, onWarning, warningThreshold = 2, enabled = true } = config;
  const { isAuthenticated, user } = useAuthStore();
  const { timeout: timeoutMinutes, isLoading: isTimeoutLoading } = useCashierWebTimeout(user?.tenantId);

  // State
  const [state, setState] = useState<SessionTimeoutState>({
    isActive: false,
    remainingTime: 0,
    isWarningShown: false,
    lastActivity: Date.now(),
  });

  // Refs for timers and tracking
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const warningTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Convert timeout from minutes to milliseconds - memoized to prevent re-renders
  const timeoutMs = useMemo(() => timeoutMinutes * 60 * 1000, [timeoutMinutes]);
  const warningMs = useMemo(() => warningThreshold * 60 * 1000, [warningThreshold]);

  /**
   * Clear all timers
   */
  const clearTimers = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
      warningTimeoutRef.current = null;
    }
  }, []);

  /**
   * Handle session timeout
   */
  const handleTimeout = useCallback(() => {
    clearTimers();
    setState(prev => ({
      ...prev,
      isActive: false,
      remainingTime: 0,
    }));
    onTimeout();
  }, [clearTimers, onTimeout]);

  /**
   * Handle warning threshold reached
   */
  const handleWarning = useCallback(() => {
    if (onWarning && !state.isWarningShown) {
      setState(prev => ({ ...prev, isWarningShown: true }));
      onWarning(warningMs);
    }
  }, [onWarning, warningMs, state.isWarningShown]);

  /**
   * Reset the session timeout
   */
  const resetTimeout = useCallback(() => {
    if (!enabled || !isAuthenticated || isTimeoutLoading) {
      return;
    }

    const now = Date.now();
    lastActivityRef.current = now;

    clearTimers();

    setState(prev => ({
      ...prev,
      isActive: true,
      remainingTime: timeoutMs,
      isWarningShown: false,
      lastActivity: now,
    }));

    // Set warning timeout
    if (onWarning && timeoutMs > warningMs) {
      warningTimeoutRef.current = setTimeout(handleWarning, timeoutMs - warningMs);
    }

    // Set main timeout
    timeoutRef.current = setTimeout(handleTimeout, timeoutMs);

    // Don't start an interval that updates every second - this causes continuous re-renders
    // Instead, we'll calculate remaining time on-demand when needed
    setState(prev => ({
      ...prev,
      isActive: true,
      remainingTime: timeoutMs,
      isWarningShown: false,
      lastActivity: now,
    }));

  }, [enabled, isAuthenticated, isTimeoutLoading, timeoutMs, warningMs, onWarning, handleWarning, handleTimeout, clearTimers]);

  /**
   * Track user activity
   */
  const trackActivity = useCallback(() => {
    if (!enabled || !isAuthenticated) {
      return;
    }
    resetTimeout();
  }, [enabled, isAuthenticated, resetTimeout]);

  /**
   * Start session timeout monitoring
   */
  const startTimeout = useCallback(() => {
    if (enabled && isAuthenticated && !isTimeoutLoading) {
      resetTimeout();
    }
  }, [enabled, isAuthenticated, isTimeoutLoading, resetTimeout]);

  /**
   * Stop session timeout monitoring
   */
  const stopTimeout = useCallback(() => {
    clearTimers();
    setState(prev => ({
      ...prev,
      isActive: false,
      remainingTime: 0,
      isWarningShown: false,
    }));
  }, [clearTimers]);

  // Activity event listeners
  useEffect(() => {
    if (!enabled || !isAuthenticated) {
      return;
    }

    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    const throttledTrackActivity = (() => {
      let lastCall = 0;
      const throttleMs = 1000; // Throttle to once per second

      return () => {
        const now = Date.now();
        if (now - lastCall >= throttleMs) {
          lastCall = now;
          trackActivity();
        }
      };
    })();

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, throttledTrackActivity, true);
    });

    // Cleanup
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, throttledTrackActivity, true);
      });
    };
  }, [enabled, isAuthenticated, trackActivity]);

  // Start timeout when authenticated
  useEffect(() => {
    if (isAuthenticated && !isTimeoutLoading) {
      startTimeout();
    } else {
      stopTimeout();
    }

    return () => {
      stopTimeout();
    };
  }, [isAuthenticated, isTimeoutLoading, startTimeout, stopTimeout, timeoutMinutes, timeoutMs, warningMs]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  // Calculate remaining time on-demand to avoid continuous re-renders
  const getCurrentRemainingTime = useCallback(() => {
    if (!state.isActive) return 0;
    const elapsed = Date.now() - lastActivityRef.current;
    return Math.max(0, timeoutMs - elapsed);
  }, [state.isActive, timeoutMs]);

  return {
    ...state,
    remainingTime: getCurrentRemainingTime(), // Calculate on-demand
    timeoutMinutes,
    resetTimeout,
    startTimeout,
    stopTimeout,
    trackActivity,
  };
};
