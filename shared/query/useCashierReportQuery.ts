// shared/query/useCashierReportQuery.ts
import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import {
  CashierReportFilters,
  CashierReportResponse,
  DEFAULT_CASHIER_REPORT_FILTERS
} from '@/shared/types/report-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';
import { generateTimePeriod } from './utils';

/**
 * Map transaction type numbers to readable action types
 */
const getActionTypeFromTransactionType = (transactionType: number): string => {
  const typeMap: { [key: number]: string } = {
    21: 'bet', // Bet transaction
    22: 'win', // Win transaction
    1: 'deposit',
    2: 'withdrawal',
    3: 'transfer',
    // Add more mappings as needed
  };
  return typeMap[transactionType] || `type_${transactionType}`;
};

/**
 * Extract market information from meta_data array
 */
const extractMarketInfo = (metaData: any[]): { marketId?: string; marketName?: string } => {
  if (!metaData || !Array.isArray(metaData) || metaData.length === 0) {
    return {};
  }

  const firstMarket = metaData[0];
  return {
    marketId: firstMarket.marketId,
    marketName: firstMarket.market
  };
};

/**
 * Generate description from meta_data array for bet transactions
 */
const generateDescriptionFromMetaData = (metaData: any[]): string => {
  if (!metaData || !Array.isArray(metaData) || metaData.length === 0) {
    return '';
  }

  // For bet transactions, create a description from the events
  const events = metaData.map(item => `${item.event} - ${item.market}: ${item.outcome}`);
  return events.join('; ');
};

/**
 * Build query string for cashier report API
 */
const buildCashierReportQuery = (filters: CashierReportFilters): string => {
  const queryParts: string[] = [];
  let timePeriod: string = generateTimePeriod();
  // Required parameters
  queryParts.push(`size=${filters.size}`);
  queryParts.push(`page=${filters.page}`);

  // Required timePeriod parameter with exact format
  if (filters.dateRange && filters.dateRange.startDate && filters.dateRange.endDate) {
    timePeriod = generateTimePeriod(filters.dateRange);
  }
  queryParts.push(`timePeriod=${timePeriod}`);

  // Default parameters
  queryParts.push(`order=${filters.order || 'desc'}`);
  queryParts.push(`sortBy=${filters.sortBy || 'created_at'}`);
  queryParts.push(`timeZone=${encodeURIComponent(filters.timeZone || 'UTC +00:00')}`);
  queryParts.push(`timeZoneName=${encodeURIComponent(filters.timeZoneName || 'UTC +00:00')}`);

  // Optional filters
  if (filters.search) {
    queryParts.push(`search=${encodeURIComponent(filters.search)}`);
  }

  if (filters.transactionId) {
    queryParts.push(`transactionId=${encodeURIComponent(filters.transactionId)}`);
  }

  if (filters.debitTransactionId) {
    queryParts.push(`debitTransactionId=${encodeURIComponent(filters.debitTransactionId)}`);
  }

  if (filters.roundId) {
    queryParts.push(`roundId=${encodeURIComponent(filters.roundId)}`);
  }

  if (filters.tenantId) {
    queryParts.push(`tenantId=${encodeURIComponent(filters.tenantId)}`);
  }

  if (filters.actionType) {
    // Transform actionType from numeric value to title string for API
    const actionTypeTitle = transformActionTypeForApi(filters.actionType);
    if (actionTypeTitle) {
      // Don't encode the array brackets and quotes - API expects raw format like ["withdraw"]
      queryParts.push(`actionType=${actionTypeTitle}`);
    }
  }

  if (filters.userId) {
    queryParts.push(`userId=${encodeURIComponent(filters.userId)}`);
  }

  if (filters.agentId) {
    queryParts.push(`agentId=${encodeURIComponent(filters.agentId)}`);
  }

  if (filters.dateTime) {
    queryParts.push(`dateTime=${encodeURIComponent(filters.dateTime)}`);
  }

  return queryParts.join('&');
};

export const fetchCashierReport = async (filters: CashierReportFilters): Promise<CashierReportResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the staging backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Build query string
  const queryString = buildCashierReportQuery(filters);

  const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch cashier report: ${response.status}`);
  }

  const apiResponse = await response.json();

  // Transform to standardized format
  const result = apiResponse.data?.data?.result;
  const rawRows = result?.rows || [];

  // Transform each row to match CashierTransaction interface
  const transformedData = rawRows.map((row: any) => {
    const marketInfo = extractMarketInfo(row.meta_data);

    return {
      id: row.internal_tracking_id,
      transactionId: row.transaction_id,
      userName: row.from_wallet_uname || row.action_by_uname || 'Unknown',
      actionType: getActionTypeFromTransactionType(row.transaction_type),
      marketId: marketInfo.marketId,
      marketName: marketInfo.marketName,
      amount: parseFloat(row.amount) || 0,
      currency: row.currency || row.source_currency_id,
      balance: parseFloat(row.ending_balance) || 0,
      status: row.status || 'unknown',
      createdAt: row.created_at,
      description: generateDescriptionFromMetaData(row.meta_data) || row.comments || 'No description',
      gameProvider: row.game_provider,
      gameType: row.game_name
    };
  });

  return {
    data: transformedData,
    success: apiResponse.success,
    message: apiResponse.data?.message || '',
    errors: apiResponse.errors || [],
    count: result?.count,
    totalPages: result?.total_pages,
    currentPage: result?.current_page
  };
};

export const useCashierReportQuery = (filters: Partial<CashierReportFilters> = {}, initialData?: CashierReportResponse | null) => {
  const { token } = useAuthStore();

  // Build final filters with defaults
  const finalFilters: CashierReportFilters = {
    ...DEFAULT_CASHIER_REPORT_FILTERS,
    ...filters,
  };

  return useQuery<CashierReportResponse>({
    queryKey: ['cashierReport', finalFilters],
    queryFn: async () => {
      try {
        return await fetchCashierReport(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated
    staleTime: 2 * 60 * 1000, // Data considered fresh for 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    initialData: initialData || undefined,
    placeholderData: keepPreviousData, // Maintain previous data during filter changes to prevent full-page skeleton loading
  });
};

// Hook for refetching cashier report with new filters
export const useCashierReportRefetch = () => {
  return (filters: Partial<CashierReportFilters> = {}) => {
    const finalFilters: CashierReportFilters = {
      ...DEFAULT_CASHIER_REPORT_FILTERS,
      ...filters,
    };

    return fetchCashierReport(finalFilters);
  };
};
