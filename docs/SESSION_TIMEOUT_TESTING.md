# Session Timeout System Testing Guide

## Overview

The session timeout system automatically logs out users after a period of inactivity. The timeout duration is fetched from the betshop-settings API using the tenant ID from the login response.

## Components

### 1. API Integration
- **Hook**: `useBetshopSettings.ts` - Fetches timeout settings from API
- **Endpoint**: `POST https://reporting.ingrandstation.com/api/v2/admin/betshop-settings`
- **Request**: `{"tenantID": <tenant_id_from_login_response>}`
- **Response**: Extracts `cashier_web_timeout` value from `data.settingsDetails` array

### 2. Session Management
- **Hook**: `useSessionTimeout.ts` - Manages timeout logic and activity tracking
- **Provider**: `SessionTimeoutProvider.tsx` - Global session timeout state management
- **Modal**: `SessionTimeoutModal.tsx` - Session expired modal component

### 3. Integration Points
- **Auth Store**: Updated to store tenant ID from login response
- **Login Mutation**: Extracts tenant ID from login response
- **PIN Validation**: Handles tenant ID in 2FA flow
- **App Provider**: Integrated into main app component hierarchy

## Testing Instructions

### 1. Manual Testing via Test Page

1. **Access Test Page**: Navigate to `/test-session-timeout` (only available when authenticated)

2. **Verify User Information**:
   - Check that user ID, email, and tenant ID are displayed
   - Ensure tenant ID is populated from login response

3. **Verify Timeout Settings**:
   - Check that timeout duration is fetched from API
   - Default fallback is 30 minutes if API fails
   - Verify API status shows "OK" or displays error message

4. **Test Activity Tracking**:
   - Move mouse, click, or type to see automatic timeout reset
   - Use "Simulate Activity" button for manual testing
   - Watch activity counter increment

5. **Test Session Progress**:
   - Monitor remaining time countdown
   - Watch progress bar color change (green → yellow → red)
   - Verify time formatting (MM:SS)

### 2. Timeout Testing

1. **Short Timeout Test**:
   - Temporarily modify timeout to 1-2 minutes for testing
   - Wait for timeout without activity
   - Verify session expired modal appears

2. **Warning Test** (if implemented):
   - Wait for warning threshold (2 minutes before expiry)
   - Verify warning modal or notification appears

3. **Modal Behavior**:
   - Verify modal is non-dismissible
   - Test "Sign In" button logs out and redirects
   - Test close button (X) also logs out and redirects
   - Test clicking outside modal also logs out and redirects

### 3. API Integration Testing

1. **Valid Tenant ID**:
   - Login with valid credentials
   - Verify tenant ID is extracted and stored
   - Check that betshop-settings API is called with correct tenant ID
   - Verify timeout value is applied

2. **Invalid/Missing Tenant ID**:
   - Test with user that has no tenant ID
   - Verify fallback to default timeout (30 minutes)
   - Check error handling

3. **API Failure**:
   - Test with network disconnected
   - Verify fallback to default timeout
   - Check error message display

### 4. Activity Tracking Testing

1. **Mouse Activity**:
   - Move mouse around the page
   - Verify timeout resets automatically

2. **Keyboard Activity**:
   - Type in any input field
   - Verify timeout resets

3. **Click Activity**:
   - Click buttons or links
   - Verify timeout resets

4. **Scroll Activity**:
   - Scroll the page
   - Verify timeout resets

### 5. Cross-Page Testing

1. **Navigation**:
   - Navigate between different pages
   - Verify session timeout continues working
   - Check that activity on any page resets timeout

2. **Modal Interactions**:
   - Open various modals
   - Verify activity within modals resets timeout

## Expected Behavior

### Normal Operation
- User activity resets timeout automatically
- Timeout duration fetched from API on login
- Session remains active during user interaction
- No interruption to user workflow

### Session Expiry
- Modal appears when session expires
- User must click "Sign In" to continue
- User is logged out and redirected to sign-in page
- All authentication state is cleared

### Error Handling
- API failures fall back to 30-minute default
- Network errors don't crash the system
- Invalid timeout values use default

## Configuration

### Default Settings
- **Default Timeout**: 30 minutes
- **Warning Threshold**: 2 minutes before expiry
- **Activity Throttle**: 1 second (prevents excessive API calls)

### Environment Variables
- `NEXT_PUBLIC_REPORTING_BACKEND_URL`: API base URL for betshop settings

## Troubleshooting

### Common Issues

1. **Timeout Not Working**:
   - Check if user is authenticated
   - Verify SessionTimeoutProvider is in app hierarchy
   - Check browser console for errors

2. **API Not Called**:
   - Verify tenant ID is present in user object
   - Check network tab for API requests
   - Verify authentication token is valid

3. **Modal Not Appearing**:
   - Check if modal is rendered in DOM
   - Verify z-index and positioning
   - Check for JavaScript errors

4. **Activity Not Tracked**:
   - Verify event listeners are attached
   - Check if activity throttling is working
   - Test different types of user activity

### Debug Information

The test page provides real-time information about:
- User authentication status
- Tenant ID presence
- API call status
- Remaining session time
- Activity tracking
- Session state

## Security Considerations

- Session timeout prevents unauthorized access to inactive sessions
- All authentication state is cleared on timeout
- User must re-authenticate after session expiry
- Timeout duration is configurable per tenant
- Activity tracking is client-side only (no sensitive data sent)

## Performance Impact

- Minimal performance impact
- Activity tracking is throttled to 1 second
- API call only made once per session
- Timer cleanup on component unmount
- No memory leaks from event listeners
